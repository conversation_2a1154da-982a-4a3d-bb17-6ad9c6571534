'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Upload, Video, FileText, AlertCircle, Check, Copy, Coins } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { useCurrentUser } from '@/hooks/use-current-user';

interface VideoUploadProps {
  className?: string;
}

export function VideoUpload({ className }: VideoUploadProps) {
  const t = useTranslations('HomePage.videoUpload');
  const currentUser = useCurrentUser();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [description, setDescription] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadedVideoUrl, setUploadedVideoUrl] = useState<string | null>(null);
  const [isCopied, setIsCopied] = useState(false);
  const [creditBalance, setCreditBalance] = useState<number | null>(null);
  const [isLoadingCredits, setIsLoadingCredits] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch user's credit balance
  useEffect(() => {
    if (currentUser) {
      fetchCreditBalance();
    }
  }, [currentUser]);

  const fetchCreditBalance = async () => {
    if (!currentUser) return;

    setIsLoadingCredits(true);
    try {
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setCreditBalance(data.balance);
      } else {
        console.error('Failed to fetch credit balance');
      }
    } catch (error) {
      console.error('Error fetching credit balance:', error);
    } finally {
      setIsLoadingCredits(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
      if (!allowedTypes.includes(file.type)) {
        setError(t('error.invalidType'));
        return;
      }

      // Validate file size (max 50MB)
      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        setError(t('error.tooLarge'));
        return;
      }

      setSelectedFile(file);
      setError(null);
      setDescription(null);
      setUploadedVideoUrl(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError(t('error.selectFirst'));
      return;
    }

    // Check if user is logged in
    if (!currentUser) {
      setError('Please log in to use this service');
      return;
    }

    // Check credit balance
    if (creditBalance === null || creditBalance < 1) {
      setError('Insufficient credits. You need at least 1 credit to describe a video.');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);
    setDescription(null);
    setUploadedVideoUrl(null);

    try {
      // Step 1: Generate a unique key for the video
      const timestamp = Date.now();
      const videoKey = `videos/${timestamp}-${selectedFile.name}`;

      setUploadProgress(10);

      // Step 2: Get presigned URL for upload
      const presignedResponse = await fetch('/api/storage/presigned-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key: videoKey,
          operation: 'putObject',
          expiresIn: 1800, // 30 minutes
        }),
      });

      if (!presignedResponse.ok) {
        const errorData = await presignedResponse.json();
        throw new Error(errorData.error || 'Failed to get upload URL');
      }

      const { url: uploadUrl } = await presignedResponse.json();
      setUploadProgress(20);

      // Step 3: Upload video directly to R2 using presigned URL
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: selectedFile,
        headers: {
          'Content-Type': selectedFile.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error(`Upload failed: ${uploadResponse.statusText}`);
      }

      setUploadProgress(60);

      // Step 4: Generate public URL for the uploaded video
      // For R2, construct the public URL based on your configuration
      const publicUrl = "https://videoprompt.app/" + videoKey// Remove query parameters
      setUploadedVideoUrl(publicUrl);

      setUploadProgress(70);

      // Step 5: Call describe-video API with the video URL
      const describeResponse = await fetch('/api/describe-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoUrl: publicUrl,
        }),
      });

      setUploadProgress(90);

      const describeData = await describeResponse.json();

      if (!describeResponse.ok) {
        // Handle insufficient credits error specifically
        if (describeResponse.status === 402) {
          setError(describeData.error || 'Insufficient credits');
          setCreditBalance(describeData.currentBalance || 0);
        } else {
          throw new Error(describeData.error || `Description failed: ${describeResponse.status}`);
        }
        return;
      }

      setUploadProgress(100);
      setDescription(describeData.description);

      // Update credit balance if returned from API
      if (typeof describeData.creditBalance === 'number') {
        setCreditBalance(describeData.creditBalance);
      }

      toast.success(t('success.processing'));
    } catch (error) {
      console.error('Error processing video:', error);
      setError(error instanceof Error ? error.message : t('error.processing'));
      toast.error(t('error.processing'));
    } finally {
      setIsUploading(false);
      // Keep progress at 100% for a moment to show completion
      setTimeout(() => setUploadProgress(0), 2000);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      // Validate file directly instead of creating synthetic event
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
      if (!allowedTypes.includes(file.type)) {
        setError(t('error.invalidType'));
        return;
      }

      const maxSize = 50 * 1024 * 1024; // 50MB
      if (file.size > maxSize) {
        setError(t('error.tooLarge'));
        return;
      }

      setSelectedFile(file);
      setError(null);
      setDescription(null);
      setUploadedVideoUrl(null);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleCopyPrompt = async () => {
    if (!description) return;

    try {
      await navigator.clipboard.writeText(description);
      setIsCopied(true);
      toast.success(t('success.copied'));

      // 2秒后重置复制状态
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      toast.error(t('error.copying'));
    }
  };

  return (
    <div className={className}>
      <Card className="w-full max-w-3xl mx-auto shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6 space-y-6">
          {/* Credit Balance Display */}
          {currentUser && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Coins className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-900">Credit Balance</p>
                    <p className="text-xs text-green-700">Each video description costs 1 credit</p>
                  </div>
                </div>
                <div className="text-right">
                  {isLoadingCredits ? (
                    <div className="w-6 h-6 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <span className="text-2xl font-bold text-green-600">
                      {creditBalance !== null ? creditBalance : '?'}
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* File Upload Area */}
          <div
            className="border-2 border-dashed border-blue-200 rounded-2xl p-8 text-center hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-300 cursor-pointer group"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('title')}
              </h3>
              <p className="text-gray-600 mb-3">
                {t('description')}
              </p>
              <p className="text-sm text-gray-500">
                {t('supportedFormats')}
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {/* Selected File Info */}
          {selectedFile && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Video className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="font-semibold text-gray-900">{selectedFile.name}</p>
                  <p className="text-blue-600 font-medium text-sm">{formatFileSize(selectedFile.size)}</p>
                </div>
              </div>
            </div>
          )}

          {/* Upload Progress */}
          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>
                  {uploadProgress < 20 ? t('progress.gettingUrl') :
                   uploadProgress < 60 ? t('progress.uploading') :
                   uploadProgress < 90 ? t('progress.generating') :
                   t('progress.finalizing')}
                </span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Uploaded Video URL */}
          {uploadedVideoUrl && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Video className="h-4 w-4 text-blue-500 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-medium text-blue-900 mb-1 text-sm">{t('success.uploaded')}</h3>
                  <p className="text-blue-800 text-xs break-all">{uploadedVideoUrl}</p>
                </div>
              </div>
            </div>
          )}

          {/* Description Result */}
          {description && (
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 shadow-sm">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <FileText className="h-4 w-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-green-900 mb-2">{t('success.generated')}</h3>
                  <div className="bg-white rounded-lg p-3 border border-green-100">
                    <p className="text-gray-800 whitespace-pre-wrap leading-relaxed text-sm">{description}</p>
                  </div>
                  <div className="mt-3 flex gap-2">
                    <Button
                      onClick={handleCopyPrompt}
                      variant="outline"
                      size="sm"
                      className="text-green-700 border-green-200 hover:bg-green-50 flex items-center gap-2"
                    >
                      {isCopied ? (
                        <>
                          <Check className="h-4 w-4" />
                          {t('copied')}
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          {t('copy')}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Upload Button */}
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || isUploading || !currentUser || (creditBalance !== null && creditBalance < 1)}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            size="lg"
          >
            {isUploading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('uploading')}
              </div>
            ) : !currentUser ? (
              'Please log in to continue'
            ) : creditBalance !== null && creditBalance < 1 ? (
              'Insufficient credits'
            ) : (
              t('button')
            )}
          </Button>

          {/* Credit insufficient warning */}
          {currentUser && creditBalance !== null && creditBalance < 1 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <p className="text-sm font-medium">
                  You need at least 1 credit to describe a video. Please purchase more credits to continue.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
