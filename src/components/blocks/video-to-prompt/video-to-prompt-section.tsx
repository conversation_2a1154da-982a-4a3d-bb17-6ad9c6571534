import { VideoUpload } from '@/components/video/video-upload';
import { useTranslations } from 'next-intl';
import { HeaderSection } from '@/components/layout/header-section';

export default function VideoToPromptSection() {
  const t = useTranslations('HomePage.videoToPrompt');

  return (
    <section id="video-to-prompt" className="py-20">
      <div className="mx-auto max-w-4xl px-6 space-y-8 md:space-y-16">
        <HeaderSection
          title={t('title')}
          subtitle={t('subtitle')}
          subtitleAs="h2"
          description={t('description')}
          descriptionAs="p"
        />
        <VideoUpload />
      </div>

    </section>

  );
}
