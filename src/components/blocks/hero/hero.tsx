import { Button } from '@/components/ui/button';
import { LocaleLink } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function HeroSection() {
  const t = useTranslations('HomePage.hero');
  
  const linkPrimary = '#video-to-prompt';
  const linkSecondary = '#features';

  return (
    <main id="hero" className="py-20 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/20 dark:from-primary/10 dark:via-primary/15 dark:to-primary/25">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          {/* title */}
          <h1 className="text-balance text-5xl font-bricolage-grotesque lg:text-6xl">
            {t.rich('title', {
              important: (chunks) => <span className="text-primary">{chunks}</span>
            })}
          </h1>

          {/* description */}
          <p className="mx-auto mt-8 max-w-3xl text-balance text-lg text-muted-foreground">
            {t('description')}
          </p>

          {/* action buttons */}
          <div className="mt-12 flex flex-row items-center justify-center gap-4">
            <div className="bg-foreground/10 rounded-[calc(var(--radius-xl)+0.125rem)] border p-0.5">
              <Button
                asChild
                size="lg"
                className="rounded-xl px-5 text-base"
              >
                <LocaleLink href={linkPrimary}>
                  <span className="text-nowrap">{t('primary')}</span>
                </LocaleLink>
              </Button>
            </div>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="h-10.5 rounded-xl px-5"
            >
              <LocaleLink href={linkSecondary}>
                <span className="text-nowrap">{t('secondary')}</span>
              </LocaleLink>
            </Button>
          </div>
        </div>
      </div>
    </main>
  );
}
