'use client';

import { Video, FileText, Zap, Download } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { HeaderSection } from '@/components/layout/header-section';

export default function VideoFeaturesSection() {
  const t = useTranslations('HomePage.features');

  const features = [
    {
      icon: Video,
      title: t('items.item-1.title'),
      description: t('items.item-1.description'),
      color: 'blue'
    },
    {
      icon: Zap,
      title: t('items.item-2.title'),
      description: t('items.item-2.description'),
      color: 'purple'
    },
    {
      icon: FileText,
      title: t('items.item-3.title'),
      description: t('items.item-3.description'),
      color: 'green'
    },
    {
      icon: Download,
      title: t('items.item-4.title'),
      description: t('items.item-4.description'),
      color: 'orange'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-100',
        icon: 'text-blue-600',
        border: 'border-blue-200'
      },
      purple: {
        bg: 'bg-purple-100',
        icon: 'text-purple-600',
        border: 'border-purple-200'
      },
      green: {
        bg: 'bg-green-100',
        icon: 'text-green-600',
        border: 'border-green-200'
      },
      orange: {
        bg: 'bg-orange-100',
        icon: 'text-orange-600',
        border: 'border-orange-200'
      }
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section id="features" className="px-4 py-16">
      <div className="mx-auto max-w-5xl px-6 space-y-8 md:space-y-16">
        <HeaderSection
          title={t('title')}
          subtitle={t('subtitle')}
          subtitleAs="h2"
          description={t('description')}
          descriptionAs="p"
        />

        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const colors = getColorClasses(feature.color);
            const Icon = feature.icon;
            
            return (
              <div
                key={index}
                className={`p-6 rounded-2xl border ${colors.border} bg-gradient-to-br from-white to-gray-50 hover:shadow-lg transition-all duration-300 hover:-translate-y-1`}
              >
                <div className={`w-12 h-12 ${colors.bg} rounded-xl flex items-center justify-center mb-4`}>
                  <Icon className={`h-6 w-6 ${colors.icon}`} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
