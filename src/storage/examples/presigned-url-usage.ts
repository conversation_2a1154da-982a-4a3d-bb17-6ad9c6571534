/**
 * Example usage of presigned URLs with the new AWS SDK implementation
 * 
 * This file demonstrates how to use the presigned URL functionality
 * that is now available with the AWS SDK v3 implementation.
 */

import { getPresignedUrl, getStorageProvider } from '../index';

/**
 * Example: Generate a presigned URL for downloading a file
 */
export async function generateDownloadUrl(fileKey: string): Promise<string> {
  try {
    const result = await getPresignedUrl({
      key: fileKey,
      expiresIn: 3600, // 1 hour
      operation: 'getObject'
    });
    
    console.log(`Download URL generated: ${result.url}`);
    console.log(`URL expires at: ${result.expiresAt}`);
    
    return result.url;
  } catch (error) {
    console.error('Failed to generate download URL:', error);
    throw error;
  }
}

/**
 * Example: Generate a presigned URL for uploading a file
 */
export async function generateUploadUrl(fileKey: string): Promise<string> {
  try {
    const result = await getPresignedUrl({
      key: fileKey,
      expiresIn: 1800, // 30 minutes
      operation: 'putObject'
    });
    
    console.log(`Upload URL generated: ${result.url}`);
    console.log(`URL expires at: ${result.expiresAt}`);
    
    return result.url;
  } catch (error) {
    console.error('Failed to generate upload URL:', error);
    throw error;
  }
}

/**
 * Example: Direct usage of the storage provider
 */
export async function directProviderUsage() {
  try {
    const provider = getStorageProvider();
    
    // Generate a download URL
    const downloadResult = await provider.getPresignedUrl({
      key: 'videos/example-video.mp4',
      expiresIn: 7200, // 2 hours
      operation: 'getObject'
    });
    
    console.log('Direct provider usage - Download URL:', downloadResult.url);
    
    // Generate an upload URL
    const uploadResult = await provider.getPresignedUrl({
      key: 'uploads/new-file.jpg',
      expiresIn: 900, // 15 minutes
      operation: 'putObject'
    });
    
    console.log('Direct provider usage - Upload URL:', uploadResult.url);
    
    return {
      downloadUrl: downloadResult.url,
      uploadUrl: uploadResult.url
    };
  } catch (error) {
    console.error('Direct provider usage failed:', error);
    throw error;
  }
}

/**
 * Example: Using presigned URL for client-side file upload
 */
export async function clientSideUploadExample(file: File, folder: string = 'uploads') {
  try {
    // Generate a unique key for the file
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    const fileKey = `${folder}/${timestamp}-${file.name}`;
    
    // Get presigned upload URL
    const uploadResult = await getPresignedUrl({
      key: fileKey,
      expiresIn: 1800, // 30 minutes
      operation: 'putObject'
    });
    
    // Upload file directly to S3 using the presigned URL
    const uploadResponse = await fetch(uploadResult.url, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });
    
    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.statusText}`);
    }
    
    console.log(`File uploaded successfully to: ${fileKey}`);
    
    // Generate a download URL for the uploaded file
    const downloadResult = await getPresignedUrl({
      key: fileKey,
      expiresIn: 3600, // 1 hour
      operation: 'getObject'
    });
    
    return {
      fileKey,
      downloadUrl: downloadResult.url,
      expiresAt: downloadResult.expiresAt
    };
  } catch (error) {
    console.error('Client-side upload failed:', error);
    throw error;
  }
}

/**
 * Example: Batch generate presigned URLs for multiple files
 */
export async function batchGenerateUrls(fileKeys: string[], expiresIn: number = 3600) {
  try {
    const provider = getStorageProvider();
    
    const urlPromises = fileKeys.map(async (key) => {
      const result = await provider.getPresignedUrl({
        key,
        expiresIn,
        operation: 'getObject'
      });
      
      return {
        key,
        url: result.url,
        expiresAt: result.expiresAt
      };
    });
    
    const results = await Promise.all(urlPromises);
    
    console.log(`Generated ${results.length} presigned URLs`);
    
    return results;
  } catch (error) {
    console.error('Batch URL generation failed:', error);
    throw error;
  }
}
