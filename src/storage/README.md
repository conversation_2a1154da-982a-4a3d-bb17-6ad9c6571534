# Storage Module

This module provides a unified interface for storing and retrieving files using various cloud storage providers. Currently, it supports Amazon S3 and compatible services like Cloudflare R2 using the official AWS SDK v3.

## Features

- Upload files to cloud storage
- Delete files from storage
- Generate presigned URLs for secure file access
- Client-side upload helpers through API endpoints
- Full AWS SDK v3 compatibility with S3 and S3-compatible services

## Basic Usage

```typescript
import { uploadFile, deleteFile, getPresignedUrl } from '@/storage';

// Upload a file
const { url, key } = await uploadFile(
  fileBuffer,
  'original-filename.jpg',
  'image/jpeg',
  'uploads/images'
);

// Delete a file
await deleteFile(key);

// Generate a presigned URL for downloading
const downloadUrl = await getPresignedUrl({
  key: 'uploads/images/file.jpg',
  expiresIn: 3600, // 1 hour
  operation: 'getObject'
});

// Generate a presigned URL for uploading
const uploadUrl = await getPresignedUrl({
  key: 'uploads/new-file.jpg',
  expiresIn: 1800, // 30 minutes
  operation: 'putObject'
});
```

## Client-Side Upload

### Option 1: Direct Upload via API (Recommended for small files)

```typescript
'use client';

import { uploadFileFromBrowser } from '@/storage/client';

// In your component
async function handleFileUpload(event) {
  const file = event.target.files[0];

  try {
    const { url, key } = await uploadFileFromBrowser(file, 'uploads/images');
    console.log('File uploaded:', url);
  } catch (error) {
    console.error('Upload failed:', error);
  }
}
```

### Option 2: Presigned URL Upload (Recommended for large files)

```typescript
'use client';

import { getPresignedUrl } from '@/storage';

async function handlePresignedUpload(file: File) {
  try {
    // Generate upload URL
    const { url } = await getPresignedUrl({
      key: `uploads/${Date.now()}-${file.name}`,
      expiresIn: 1800, // 30 minutes
      operation: 'putObject'
    });

    // Upload directly to S3
    const response = await fetch(url, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (response.ok) {
      console.log('File uploaded successfully');
    }
  } catch (error) {
    console.error('Upload failed:', error);
  }
}
```

## Configuration

The storage module is configured in two ways:

1. In `src/config/website.tsx`:

```typescript
// In src/config/website.tsx
export const websiteConfig = {
  // ...other config
  storage: {
    provider: 's3',
  },
  // ...other config
}
```

2. Using environment variables:

```
# Required
STORAGE_REGION=auto
STORAGE_ACCESS_KEY_ID=your-access-key
STORAGE_SECRET_ACCESS_KEY=your-secret-key
STORAGE_BUCKET_NAME=your-bucket-name
STORAGE_ENDPOINT=https://custom-endpoint.com
STORAGE_PUBLIC_URL=https://cdn.example.com
```

**Note**: When using S3-compatible services like Cloudflare R2, the `STORAGE_ENDPOINT` is required. Set `STORAGE_FORCE_PATH_STYLE=true` for better compatibility with S3-compatible services.

## Advanced Usage

### Using the Storage Provider Directly

If you need more control, you can interact with the storage provider directly:

```typescript
import { getStorageProvider } from '@/storage';

const provider = getStorageProvider();

// Use provider methods directly
const result = await provider.uploadFile({
  file: fileBuffer,
  filename: 'example.pdf',
  contentType: 'application/pdf',
  folder: 'documents'
});
```

### Using a Custom Provider Implementation

You can create and use your own storage provider implementation:

```typescript
import { StorageProvider, UploadFileParams, UploadFileResult } from '@/storage/types';

class CustomStorageProvider implements StorageProvider {
  // Implement the required methods
  async uploadFile(params: UploadFileParams): Promise<UploadFileResult> {
    // Your implementation
    return { url: 'https://example.com/file.jpg', key: 'file.jpg' };
  }

  async deleteFile(key: string): Promise<void> {
    // Your implementation
  }

  getProviderName(): string {
    return 'CustomProvider';
  }
}

// Then use it
const customProvider = new CustomStorageProvider();
const result = await customProvider.uploadFile({
  file: fileBuffer,
  filename: 'example.jpg',
  contentType: 'image/jpeg'
});
```

## Presigned URLs

The storage module now supports presigned URLs for secure, direct client-to-storage uploads and downloads:

### Benefits of Presigned URLs

- **Security**: Temporary, time-limited access to specific files
- **Performance**: Direct uploads/downloads bypass your server
- **Scalability**: Reduces server load for file operations
- **Flexibility**: Support for both upload and download operations

### Usage Examples

See the complete examples in `src/storage/examples/presigned-url-usage.ts` for detailed usage patterns including:

- Generating download URLs
- Generating upload URLs
- Client-side direct uploads
- Batch URL generation

## API Reference

### Server-Side Functions

For server-side usage (in API routes, server actions, etc.):

- `uploadFile(file, filename, contentType, folder?)`: Upload a file to storage
- `deleteFile(key)`: Delete a file from storage
- `getPresignedUrl(params)`: Generate a presigned URL for file access

### Client-Side Functions

For client-side usage (in React components with 'use client'):

- `uploadFileFromBrowser(file, folder?)`: Upload a file from the browser (via API)
- `getPresignedUrl(params)`: Generate presigned URLs (can be used client-side)

**Note**: Import client-side functions from `@/storage/client` to avoid Node.js module conflicts in the browser.

### Provider Interface

The `StorageProvider` interface defines the following methods:

- `uploadFile(params)`: Upload a file to storage
- `deleteFile(key)`: Delete a file from storage
- `getPresignedUrl(params)`: Generate a presigned URL for file access
- `getProviderName()`: Get the provider name

### Configuration

The `StorageConfig` interface defines the configuration options:

- `region`: Storage region (e.g., 'auto' for Cloudflare R2)
- `endpoint?`: Custom endpoint URL for S3-compatible services
- `accessKeyId`: Access key ID for authentication
- `secretAccessKey`: Secret access key for authentication
- `bucketName`: Storage bucket name
- `publicUrl?`: Public URL for accessing files
- `forcePathStyle?`: Force path-style URLs (recommended for S3-compatible services)
