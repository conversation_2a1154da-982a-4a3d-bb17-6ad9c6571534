/**
 * Storage configuration
 */
export interface StorageConfig {
  region: string;
  endpoint?: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicUrl?: string;
  forcePathStyle?: boolean;
}

/**
 * Storage provider error types
 */
export class StorageError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'StorageError';
  }
}

export class ConfigurationError extends StorageError {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigurationError';
  }
}

export class UploadError extends StorageError {
  constructor(message: string) {
    super(message);
    this.name = 'UploadError';
  }
}

/**
 * Upload file parameters
 */
export interface UploadFileParams {
  file: Buffer | Blob;
  filename: string;
  contentType: string;
  folder?: string;
}

/**
 * Upload file result
 */
export interface UploadFileResult {
  url: string;
  key: string;
}

/**
 * Presigned URL parameters
 */
export interface GetPresignedUrlParams {
  key: string;
  expiresIn?: number; // seconds, default 3600 (1 hour)
  operation?: 'getObject' | 'putObject'; // default 'getObject'
}

/**
 * Presigned URL result
 */
export interface GetPresignedUrlResult {
  url: string;
  expiresAt: Date;
}

/**
 * Storage provider interface
 */
export interface StorageProvider {
  /**
   * Upload a file to storage
   */
  uploadFile(params: UploadFileParams): Promise<UploadFileResult>;

  /**
   * Delete a file from storage
   */
  deleteFile(key: string): Promise<void>;

  /**
   * Get a presigned URL for accessing a file
   */
  getPresignedUrl(params: GetPresignedUrlParams): Promise<GetPresignedUrlResult>;

  /**
   * Get the provider's name
   */
  getProviderName(): string;
}
