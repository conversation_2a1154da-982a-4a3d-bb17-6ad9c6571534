import { randomUUID } from 'crypto';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { storageConfig } from '../config/storage-config';
import {
  ConfigurationError,
  type StorageConfig,
  StorageError,
  type StorageProvider,
  UploadError,
  type UploadFileParams,
  type UploadFileResult,
  type GetPresignedUrlParams,
  type GetPresignedUrlResult,
} from '../types';

/**
 * Amazon S3 storage provider implementation using AWS SDK v3
 *
 * docs:
 * https://mksaas.com/docs/storage
 *
 * This provider works with Amazon S3 and compatible services like Cloudflare R2
 * using the official AWS SDK v3 for better compatibility and features like presigned URLs
 * https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/client/s3/
 * https://developers.cloudflare.com/r2/
 */
export class S3Provider implements StorageProvider {
  private config: StorageConfig;
  private s3Client: S3Client | null = null;

  constructor(config: StorageConfig = storageConfig) {
    this.config = config;
  }

  /**
   * Get the provider name
   */
  public getProviderName(): string {
    return 'S3';
  }

  /**
   * Get the S3 client instance
   */
  private getS3Client(): S3Client {
    if (this.s3Client) {
      return this.s3Client;
    }

    const { region, endpoint, accessKeyId, secretAccessKey, bucketName, forcePathStyle } =
      this.config;

    if (!region) {
      throw new ConfigurationError('Storage region is not configured');
    }

    if (!accessKeyId || !secretAccessKey) {
      throw new ConfigurationError('Storage credentials are not configured');
    }

    if (!bucketName) {
      throw new ConfigurationError('Storage bucket name is not configured');
    }

    // AWS SDK v3 client configuration
    const clientConfig: any = {
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    };

    // Add endpoint if provided (for S3-compatible services like Cloudflare R2)
    if (endpoint) {
      clientConfig.endpoint = endpoint;
      // Force path style for S3-compatible services
      clientConfig.forcePathStyle = forcePathStyle !== false;
    }

    this.s3Client = new S3Client(clientConfig);

    return this.s3Client;
  }

  /**
   * Generate a unique filename with the original extension
   */
  private generateUniqueFilename(originalFilename: string): string {
    const extension = originalFilename.split('.').pop() || '';
    const uuid = randomUUID();
    return `${uuid}${extension ? `.${extension}` : ''}`;
  }

  /**
   * Upload a file to S3
   */
  public async uploadFile(params: UploadFileParams): Promise<UploadFileResult> {
    try {
      const { file, filename, contentType, folder } = params;
      const s3 = this.getS3Client();
      const { bucketName } = this.config;

      const uniqueFilename = this.generateUniqueFilename(filename);
      const key = folder ? `${folder}/${uniqueFilename}` : uniqueFilename;

      // Convert Blob to Buffer if needed
      let fileContent: Buffer;
      if (file instanceof Blob) {
        fileContent = Buffer.from(await file.arrayBuffer());
      } else {
        fileContent = file;
      }

      // Upload the file using AWS SDK v3
      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: fileContent,
        ContentType: contentType,
      });

      await s3.send(command);

      // Generate the URL
      const { publicUrl, endpoint } = this.config;
      let url: string;

      if (publicUrl) {
        // Use custom domain if provided
        url = `${publicUrl.replace(/\/$/, '')}/${key}`;
        console.log('uploadFile, public url', url);
      } else if (endpoint) {
        // For S3-compatible services, construct URL manually
        url = `${endpoint.replace(/\/$/, '')}/${bucketName}/${key}`;
        console.log('uploadFile, constructed url', url);
      } else {
        // For standard AWS S3
        url = `https://${bucketName}.s3.${this.config.region}.amazonaws.com/${key}`;
        console.log('uploadFile, aws s3 url', url);
      }

      return { url, key };
    } catch (error) {
      if (error instanceof ConfigurationError) {
        console.error('uploadFile, configuration error', error);
        throw error;
      }

      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error occurred during file upload';
      console.error('uploadFile, error', message);
      throw new UploadError(message);
    }
  }

  /**
   * Delete a file from S3
   */
  public async deleteFile(key: string): Promise<void> {
    try {
      const s3 = this.getS3Client();
      const { bucketName } = this.config;

      const command = new DeleteObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      await s3.send(command);
      console.log(`File with key ${key} deleted successfully`);
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error occurred during file deletion';
      console.error('deleteFile, error', message);
      throw new StorageError(message);
    }
  }

  /**
   * Get a presigned URL for accessing a file
   */
  public async getPresignedUrl(params: GetPresignedUrlParams): Promise<GetPresignedUrlResult> {
    try {
      const { key, expiresIn = 3600, operation = 'getObject' } = params;
      const s3 = this.getS3Client();
      const { bucketName } = this.config;

      let command;
      if (operation === 'putObject') {
        command = new PutObjectCommand({
          Bucket: bucketName,
          Key: key,
        });
      } else {
        command = new GetObjectCommand({
          Bucket: bucketName,
          Key: key,
        });
      }

      const url = await getSignedUrl(s3, command, { expiresIn });
      const expiresAt = new Date(Date.now() + expiresIn * 1000);

      return { url, expiresAt };
    } catch (error) {
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error occurred during presigned URL generation';
      console.error('getPresignedUrl, error', message);
      throw new StorageError(message);
    }
  }
}
