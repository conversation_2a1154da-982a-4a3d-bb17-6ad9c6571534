import type { UploadFileResult, GetPresignedUrlParams, GetPresignedUrlResult } from './types';

const API_STORAGE_UPLOAD = '/api/storage/upload';
const API_STORAGE_PRESIGNED_URL = '/api/storage/presigned-url';

/**
 * Uploads a file from the browser to the storage provider
 * This function is meant to be used in client components
 *
 * Note: With AWS SDK v3, we now support presigned URLs for direct uploads.
 * For now, this function uses the direct upload API endpoint, but it can be
 * enhanced to use presigned URLs for better performance with large files.
 *
 * @param file - The file object from an input element
 * @param folder - Optional folder path to store the file in
 * @returns Promise with the URL of the uploaded file
 */
export const uploadFileFromBrowser = async (
  file: File,
  folder?: string
): Promise<UploadFileResult> => {
  try {
    // Using direct upload through API endpoint
    // TODO: Consider implementing presigned URL upload for large files
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', folder || '');

    const response = await fetch(API_STORAGE_UPLOAD, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = (await response.json()) as { message: string };
      throw new Error(error.message || 'Failed to upload file');
    }

    return await response.json();
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Unknown error occurred during file upload';
    throw new Error(message);
  }
};

/**
 * Generate a presigned URL from the browser
 * This function is meant to be used in client components
 *
 * @param params - Parameters for generating the presigned URL
 * @returns Promise with the presigned URL and expiration date
 */
export const getPresignedUrlFromBrowser = async (
  params: GetPresignedUrlParams
): Promise<GetPresignedUrlResult> => {
  try {
    const response = await fetch(API_STORAGE_PRESIGNED_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const error = (await response.json()) as { error: string };
      throw new Error(error.error || 'Failed to generate presigned URL');
    }

    return await response.json();
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Unknown error occurred during presigned URL generation';
    throw new Error(message);
  }
};

/**
 * Upload a file directly to storage using a presigned URL
 * This bypasses the server and uploads directly to S3
 *
 * @param file - The file to upload
 * @param key - The storage key for the file
 * @param expiresIn - URL expiration time in seconds (default: 1800 = 30 minutes)
 * @returns Promise with the file key and public URL
 */
export const uploadFileWithPresignedUrl = async (
  file: File,
  key: string,
  expiresIn: number = 1800
): Promise<{ key: string; url: string }> => {
  try {
    // Generate presigned upload URL
    const { url: presignedUrl } = await getPresignedUrlFromBrowser({
      key,
      operation: 'putObject',
      expiresIn,
    });

    // Upload file directly to S3
    const uploadResponse = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.statusText}`);
    }

    // Construct the public URL (this is a simplified version)
    // In a real implementation, you might want to use the storage config
    const publicUrl = presignedUrl.split('?')[0]; // Remove query parameters

    return { key, url: publicUrl };
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Unknown error occurred during presigned URL upload';
    throw new Error(message);
  }
};
