import CallToActionSection from '@/components/blocks/calltoaction/calltoaction';
import FaqSection from '@/components/blocks/faqs/faqs';
import HeroSection from '@/components/blocks/hero/hero';
import StatsSection from '@/components/blocks/stats/stats';
import VideoFeaturesSection from '@/components/blocks/video-features/video-features';
import VideoToPromptSection from '@/components/blocks/video-to-prompt/video-to-prompt-section';
import { NewsletterCard } from '@/components/newsletter/newsletter-card';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';

/**
 * https://next-intl.dev/docs/environments/actions-metadata-route-handlers#metadata-api
 */
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });

  return constructMetadata({
    title: t('title'),
    description: t('description'),
    canonicalUrl: getUrlWithLocale('', locale),
  });
}

export default async function HomePage() {
  return (
    <>
      <div className="flex flex-col">
        <HeroSection />

        {/* Video to Prompt Section - Main Feature */}
        <VideoToPromptSection />

        {/* Features Section */}
        <VideoFeaturesSection />

        {/* Stats Section */}
        <StatsSection />

        {/* FAQ Section */}
        <FaqSection />

        {/* Call to Action */}
        <CallToActionSection />

        {/* Newsletter */}
        {/* <NewsletterCard /> */}
      </div>
    </>
  );
}
