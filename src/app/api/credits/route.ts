import { type NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/server';
import { getUserCreditBalance, getCreditHistory, initializeUserCredit } from '@/lib/credit-service';

/**
 * GET /api/credits
 * Get current user's credit balance and recent history
 */
export async function GET(req: NextRequest) {
  try {
    // Check user authentication
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.id;
    const { searchParams } = new URL(req.url);
    const includeHistory = searchParams.get('history') === 'true';
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get credit balance
    let creditBalance = await getUserCreditBalance(userId);
    
    // Initialize credit if not exists
    if (!creditBalance) {
      creditBalance = await initializeUserCredit(userId, 5);
    }

    const response: any = {
      balance: creditBalance.balance,
      createdAt: creditBalance.createdAt,
      updatedAt: creditBalance.updatedAt,
    };

    // Include history if requested
    if (includeHistory) {
      const history = await getCreditHistory(userId, limit, offset);
      response.history = history;
    }

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error getting credit info:', error);
    return NextResponse.json(
      { error: 'Failed to get credit information' },
      { status: 500 }
    );
  }
}
