import { type NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/server';
import { spendCredits, getUserCreditBalance } from '@/lib/credit-service';

/**
 * Intended to be slightly less than the maximum execution time allowed by the
 * runtime so that we can gracefully terminate our request.
 */
const TIMEOUT_MILLIS = 100 * 1000;

// Qwen API configuration
const QWEN_API_KEY = process.env.QWEN_API_KEY;
const QWEN_BASE_URL = process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1';
const QWEN_MODEL = process.env.QWEN_MODEL || 'qwen2.5-vl-32b-instruct';

const withTimeout = <T>(
  promise: Promise<T>,
  timeoutMillis: number
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error('Request timed out')), timeoutMillis)
    ),
  ]);
};

export async function POST(req: NextRequest) {
  const requestId = Math.random().toString(36).substring(7);

  try {
    // Check user authentication
    const session = await getSession();
    if (!session?.user) {
      console.error(`Unauthorized request [requestId=${requestId}]`);
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.id;
    console.log(`Processing request for user ${userId} [requestId=${requestId}]`);

    // Check user's credit balance
    const creditBalance = await getUserCreditBalance(userId);
    if (!creditBalance || creditBalance.balance < 1) {
      console.error(`Insufficient credits for user ${userId} [requestId=${requestId}]`);
      return NextResponse.json({
        error: 'Insufficient credits. You need at least 1 credit to describe a video.',
        currentBalance: creditBalance?.balance || 0
      }, { status: 402 }); // 402 Payment Required
    }

    const body = await req.json();
    const videoUrl = body.videoUrl;

    if (!videoUrl) {
      const error = 'No video URL provided';
      console.error(`${error} [requestId=${requestId}]`);
      return NextResponse.json({ error }, { status: 400 });
    }

    // Validate URL format
    try {
      new URL(videoUrl);
    } catch {
      const error = 'Invalid video URL format';
      console.error(`${error} [requestId=${requestId}]`);
      return NextResponse.json({ error }, { status: 400 });
    }

    console.log(`Processing video URL [requestId=${requestId}, url=${videoUrl}]`);

    const startTime = performance.now();

    // Generate description using Qwen model via direct API call
    const generatePromise = async () => {
      const response = await fetch(`${QWEN_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${QWEN_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: QWEN_MODEL,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'video_url',
                  video_url: videoUrl,
                },
                {
                  type: 'text',
                  text: 'Describe the video',
                },
              ],
            },
          ],
        }),
      });

      if (!response.ok) {
        console.log(response);
        const errorText = await response.text();
        throw new Error(`Qwen API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('Invalid response format from Qwen API');
      }

      const elapsed = (performance.now() - startTime) / 1000;
      console.log(
        `Completed video description [requestId=${requestId}, elapsed=${elapsed.toFixed(1)}s]`
      );

      return {
        description: data.choices[0].message.content,
      };
    };

    const result = await withTimeout(generatePromise(), TIMEOUT_MILLIS);

    // Deduct 1 credit after successful processing
    const creditResult = await spendCredits(
      userId,
      1,
      `Video description - ${videoUrl}`,
      requestId,
      'video_description'
    );

    if (!creditResult.success) {
      console.error(`Failed to deduct credit for user ${userId} [requestId=${requestId}]: ${creditResult.error}`);
      // Still return the result but log the error
      // In production, you might want to handle this differently
    } else {
      console.log(`Credit deducted for user ${userId}, new balance: ${creditResult.balance} [requestId=${requestId}]`);
    }

    return NextResponse.json({
      ...result,
      creditBalance: creditResult.balance || creditBalance.balance - 1
    }, { status: 200 });

  } catch (error) {
    // Log full error detail on the server, but return a generic error message
    // to avoid leaking any sensitive information to the client.
    console.error(
      `Error describing video [requestId=${requestId}]: `,
      error
    );
    
    return NextResponse.json(
      {
        error: 'Failed to describe video. Please try again later.',
      },
      { status: 500 }
    );
  }
}

// No special configuration needed for URL-based requests
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb', // Small limit since we only accept JSON with URL
    },
  },
};
