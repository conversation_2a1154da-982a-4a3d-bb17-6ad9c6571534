import { getPresignedUrl } from '@/storage';
import { StorageError } from '@/storage/types';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * Generate a presigned URL for file upload or download
 * 
 * POST /api/storage/presigned-url
 * 
 * Body:
 * {
 *   "key": "uploads/file.jpg",
 *   "operation": "getObject" | "putObject",
 *   "expiresIn": 3600
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { key, operation = 'getObject', expiresIn = 3600 } = body;

    // Validate required fields
    if (!key || typeof key !== 'string') {
      return NextResponse.json(
        { error: 'Key is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate operation
    if (operation !== 'getObject' && operation !== 'putObject') {
      return NextResponse.json(
        { error: 'Operation must be either "getObject" or "putObject"' },
        { status: 400 }
      );
    }

    // Validate expiresIn
    if (typeof expiresIn !== 'number' || expiresIn <= 0 || expiresIn > 86400) {
      return NextResponse.json(
        { error: 'expiresIn must be a number between 1 and 86400 (24 hours)' },
        { status: 400 }
      );
    }

    // Generate presigned URL
    const result = await getPresignedUrl({
      key,
      operation,
      expiresIn,
    });

    console.log(`Generated ${operation} presigned URL for key: ${key}`);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating presigned URL:', error);

    if (error instanceof StorageError) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: 'Something went wrong while generating the presigned URL' },
      { status: 500 }
    );
  }
}

/**
 * Generate a presigned URL for file download via GET request
 * 
 * GET /api/storage/presigned-url?key=uploads/file.jpg&expiresIn=3600
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');
    const expiresInParam = searchParams.get('expiresIn');
    const expiresIn = expiresInParam ? parseInt(expiresInParam, 10) : 3600;

    // Validate required fields
    if (!key) {
      return NextResponse.json(
        { error: 'Key parameter is required' },
        { status: 400 }
      );
    }

    // Validate expiresIn
    if (isNaN(expiresIn) || expiresIn <= 0 || expiresIn > 86400) {
      return NextResponse.json(
        { error: 'expiresIn must be a number between 1 and 86400 (24 hours)' },
        { status: 400 }
      );
    }

    // Generate presigned URL for download
    const result = await getPresignedUrl({
      key,
      operation: 'getObject',
      expiresIn,
    });

    console.log(`Generated download presigned URL for key: ${key}`);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating presigned URL:', error);

    if (error instanceof StorageError) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: 'Something went wrong while generating the presigned URL' },
      { status: 500 }
    );
  }
}
