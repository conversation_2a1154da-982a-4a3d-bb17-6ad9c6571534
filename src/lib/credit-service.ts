import 'server-only';

import { getDb } from '@/db';
import { credits, creditLogs } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { randomUUID } from 'crypto';

export interface CreditBalance {
  userId: string;
  balance: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreditLog {
  id: string;
  userId: string;
  type: string;
  amount: number;
  description?: string;
  balanceBefore: number;
  balanceAfter: number;
  createdAt: Date;
}

export interface CreditTransaction {
  success: boolean;
  balance?: number;
  error?: string;
}

/**
 * Credit operation types
 */
export const CreditTypes = {
  SUBSCRIPTION: 'subscription',
  CHECKIN: 'checkin', 
  SPEND: 'spend',
  ADMIN_ADJUSTMENT: 'admin_adjustment',
} as const;

/**
 * Get user's current credit balance
 */
export async function getUserCreditBalance(userId: string): Promise<CreditBalance | null> {
  try {
    const db = await getDb();
    const result = await db
      .select()
      .from(credits)
      .where(eq(credits.userId, userId))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    return {
      userId: result[0].userId,
      balance: result[0].balance,
      createdAt: result[0].createdAt,
      updatedAt: result[0].updatedAt,
    };
  } catch (error) {
    console.error('Error getting user credit balance:', error);
    throw new Error('Failed to get credit balance');
  }
}

/**
 * Initialize credit balance for a new user
 */
export async function initializeUserCredit(
  userId: string, 
  initialBalance: number = 5
): Promise<CreditBalance> {
  try {
    const db = await getDb();
    
    // Check if user already has a credit record
    const existing = await getUserCreditBalance(userId);
    if (existing) {
      return existing;
    }

    const now = new Date();
    const creditId = randomUUID();

    // Create credit record
    const [creditRecord] = await db
      .insert(credits)
      .values({
        id: creditId,
        userId,
        balance: initialBalance,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    // Create initial log entry
    if (initialBalance > 0) {
      await db.insert(creditLogs).values({
        id: randomUUID(),
        userId,
        type: CreditTypes.CHECKIN,
        amount: initialBalance,
        description: 'Initial credit bonus',
        balanceBefore: 0,
        balanceAfter: initialBalance,
        createdAt: now,
      });
    }

    return {
      userId: creditRecord.userId,
      balance: creditRecord.balance,
      createdAt: creditRecord.createdAt,
      updatedAt: creditRecord.updatedAt,
    };
  } catch (error) {
    console.error('Error initializing user credit:', error);
    throw new Error('Failed to initialize credit');
  }
}

/**
 * Spend credits (atomic transaction)
 */
export async function spendCredits(
  userId: string,
  amount: number,
  description?: string,
  referenceId?: string,
  referenceType?: string
): Promise<CreditTransaction> {
  if (amount <= 0) {
    return { success: false, error: 'Amount must be positive' };
  }

  try {
    const db = await getDb();
    
    return await db.transaction(async (tx) => {
      // Get current balance with row lock
      const [currentCredit] = await tx
        .select()
        .from(credits)
        .where(eq(credits.userId, userId))
        .for('update');

      if (!currentCredit) {
        // Initialize credit if not exists
        await initializeUserCredit(userId);
        return { success: false, error: 'Insufficient credits' };
      }

      if (currentCredit.balance < amount) {
        return { success: false, error: 'Insufficient credits' };
      }

      const newBalance = currentCredit.balance - amount;
      const now = new Date();

      // Update balance
      await tx
        .update(credits)
        .set({
          balance: newBalance,
          updatedAt: now,
        })
        .where(eq(credits.userId, userId));

      // Create log entry
      await tx.insert(creditLogs).values({
        id: randomUUID(),
        userId,
        type: CreditTypes.SPEND,
        amount: -amount, // Negative for spending
        description: description || 'Credit spent',
        balanceBefore: currentCredit.balance,
        balanceAfter: newBalance,
        createdAt: now,
      });

      return { success: true, balance: newBalance };
    });
  } catch (error) {
    console.error('Error spending credits:', error);
    return { success: false, error: 'Failed to spend credits' };
  }
}

/**
 * Add credits (atomic transaction)
 */
export async function addCredits(
  userId: string,
  amount: number,
  type: string = CreditTypes.ADMIN_ADJUSTMENT,
  description?: string
): Promise<CreditTransaction> {
  if (amount <= 0) {
    return { success: false, error: 'Amount must be positive' };
  }

  try {
    const db = await getDb();
    
    return await db.transaction(async (tx) => {
      // Get or create credit record
      let currentCredit = await getUserCreditBalance(userId);
      if (!currentCredit) {
        currentCredit = await initializeUserCredit(userId, 0);
      }

      const newBalance = currentCredit.balance + amount;
      const now = new Date();

      // Update balance
      await tx
        .update(credits)
        .set({
          balance: newBalance,
          updatedAt: now,
        })
        .where(eq(credits.userId, userId));

      // Create log entry
      await tx.insert(creditLogs).values({
        id: randomUUID(),
        userId,
        type,
        amount,
        description: description || 'Credits added',
        balanceBefore: currentCredit.balance,
        balanceAfter: newBalance,
        createdAt: now,
      });

      return { success: true, balance: newBalance };
    });
  } catch (error) {
    console.error('Error adding credits:', error);
    return { success: false, error: 'Failed to add credits' };
  }
}

/**
 * Get user's credit transaction history
 */
export async function getCreditHistory(
  userId: string,
  limit: number = 50,
  offset: number = 0
): Promise<CreditLog[]> {
  try {
    const db = await getDb();
    const result = await db
      .select()
      .from(creditLogs)
      .where(eq(creditLogs.userId, userId))
      .orderBy(sql`${creditLogs.createdAt} DESC`)
      .limit(limit)
      .offset(offset);

    return result.map(log => ({
      id: log.id,
      userId: log.userId,
      type: log.type,
      amount: log.amount,
      description: log.description || undefined,
      balanceBefore: log.balanceBefore,
      balanceAfter: log.balanceAfter,
      createdAt: log.createdAt,
    }));
  } catch (error) {
    console.error('Error getting credit history:', error);
    throw new Error('Failed to get credit history');
  }
}
