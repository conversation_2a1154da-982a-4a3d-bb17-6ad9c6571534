{"i18n-ally.localesPaths": ["messages", "src/i18n"], "i18n-ally.keystyle": "nested", "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "search.exclude": {"**/node_modules": true, ".next": true, ".source": true, ".wrangler": true, ".open-next": true}}